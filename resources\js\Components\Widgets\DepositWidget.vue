<style>
    .item-sombra {
            position: relative;

        }

        .item-sombra::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px; /* <PERSON><PERSON><PERSON> da sombra */
            height: 50px;
            background: linear-gradient(to right, rgba(240, 240, 240, 0), #323637e7); /* Gradiente de transparência */


        }
    #placeholder-input::placeholder {
        color: white;
    }
    .texto-valor {
        font-size: 15px;
    }
    @media (max-width:768px) {
        .loading-mobile-qr {
            margin-top: 30vh;
        }
        .qr-container {
            padding: 10px;
        }
        .qr-code-wrapper {
            max-width: 200px !important;
            margin: 0 auto;
        }
        .qr-value-display {
            font-size: 1.5em !important;
            margin-bottom: 8px !important;
        }
        .qr-input-field {
            font-size: 11px !important;
            padding: 8px !important;
        }
        .qr-copy-button {
            max-width: 250px !important;
            font-size: 12px !important;
            padding: 10px !important;
        }
    }
    @media (max-width:600px) {
        .texto-valor {
            font-size: 12px;
        }
        .qr-code-wrapper {
            max-width: 180px !important;
        }
        .qr-value-display {
            font-size: 1.2em !important;
        }
    }
</style>

<template>
        <div v-if="paymentType === 'stripe' && publishableKey && setting && setting.stripe_is_enable" class="p-4">
            <stripe-checkout ref="checkoutRef" :pk="publishableKey" :sessionId="sessionId" />
            <div class="flex w-full mt-3 mb-3">
                <div class="w-36 mr-2">
                    <label for="currency" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ $t('Currency') }}</label>
                    <select id="currency" v-model="currency" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="USD">$ {{ $t('Dollar') }}</option>
                        <option value="BRL">R$ {{ $t('Real') }}</option>
                    </select>
                </div>
                <div class="w-full">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ $t('Amount') }}</label>
                    <input type="number"
                           v-model="amount"
                           class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                           :min="setting.min_deposit"
                           :max="setting.max_deposit"
                           :placeholder="$t('0,00')"
                           required
                    >
                </div>
            </div>

            <button :disabled="!sessionId" @click.prevent="checkoutStripe" class="ui-button-blue rounded w-full">{{ $t('Pay With Stripe') }}</button>
        </div>
        <div v-if="setting && paymentType === 'pix' && (setting.suitpay_is_enable || setting.mercadopago_is_enable || setting.digitopay_is_enable)">
            <div v-if="showPixQRCode && wallet" class="flex flex-col ">
                <div class="w-full">
                    <div class="flex justify-center">
                        <p style="text-align: center">Escaneie a imagem <br> para realizar o pagamento</p>
                    </div>
                </div>
                <div class="w-full p-4 qr-container">
                    <div class="p-3 flex justify-center items-center qr-code-wrapper" style="max-width: 250px;margin: 0 auto;">
                        <QRCodeVue3 :value="qrcodecopypast"/>
                    </div>
                    <div class="mt-4 p-4 rounded-lg" style="text-align: center;border: 1px dashed rgba(253, 255, 255, .6);" >
                        <p class="font-bold qr-value-display" style="font-size: 2em;color: var(--ci-primary-color);margin-bottom: 10px">{{ state.currencyFormat(parseFloat(deposit.amount), wallet.currency) }}</p>
                        <input style="background-color: #424344;font-size: 12px;" id="pixcopiaecola" type="text" class="input appearance-none border border-gray-300 rounded-md bg-transparent border-none w-full qr-input-field" v-model="qrcodecopypast">

                        <div class="mt-5 w-full flex items-center justify-center">
                            <button @click.prevent="copyQRCode" type="button" class="ui-button-blue w-full qr-copy-button" style="background-color: var(--ci-primary-opacity-color);color: var(--ci-primary-color);max-width: 270px;">
                                <span class="uppercase text-sm">{{ $t('Copiar Código "copia e cola"') }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="!showPixQRCode">
                <div v-if="setting != null && wallet != null && isLoading === false" class="flex flex-col w-full">
                    <form action="" @submit.prevent="submitQRCode">
                        <div class="p-4" style="background-color: #323637;border-radius: 5px">
                            <div class="w-full flex items-center justify-between rounded">
                                <div class="flex w-full items-center">
                                    <!---   <img :src="`/assets/images/pix.png`" alt="" width="100"> -->
                                    <img style="max-width: 70px" src="https://logospng.org/download/pix/logo-pix-1024.png" alt="">
                                </div>
                                <div class="w-8 ">
                                    <i style="opacity: .5;" class="fa-solid fa-chevron-down"></i>
                                </div>
                            </div>
                            <div class="w-full" style="height: 1px;background-color: #424344;"></div>
                            <div class="mt-3">
                                <p class="text-gray-500" style="font-weight: bold;font-size: .90rem;color: white;">Valor a ser depositado:</p>
                                <div class="w-full flex items-center justify-between rounded py-1">
                                    <div class="flex w-full items-center">
                                        <i style="position: absolute;padding-left: 20px;font-size: 14px;z-index: 2;" class="fa-solid fa-brazilian-real-sign"></i>
                                        <div style="position: absolute;right: 0;font-size: 10px;z-index: 2;padding-right: 50px;display: flex;align-items: center;font-weight: bold;gap: 3px;" >
                                            <svg style="max-width: 10px;" xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512"><mask id="a"><circle cx="256" cy="256" r="256" fill="#fff"></circle></mask><g mask="url(#a)"><path fill="#6da544" d="M0 0h512v512H0z"></path><path fill="#ffda44" d="M256 100.2 467.5 256 256 411.8 44.5 256z"></path><path fill="#eee" d="M174.2 221a87 87 0 0 0-7.2 36.3l162 49.8a88.5 88.5 0 0 0 14.4-34c-40.6-65.3-119.7-80.3-169.1-52z"></path><path fill="#0052b4" d="M255.7 167a89 89 0 0 0-41.9 10.6 89 89 0 0 0-39.6 43.4 181.7 181.7 0 0 1 169.1 52.2 89 89 0 0 0-9-59.4 89 89 0 0 0-78.6-46.8zM212 250.5a149 149 0 0 0-45 6.8 89 89 0 0 0 10.5 40.9 89 89 0 0 0 120.6 36.2 89 89 0 0 0 30.7-27.3A151 151 0 0 0 212 250.5z"></path></g></svg><p>BRL</p></div>
                                            <div style="position: absolute;right: 0;padding-right: 100px;z-index: 2;font-size: 12px;color: #A7F432;" v-if="deposit.amount > 0" class="text-right">
                                                + {{ state.currencyFormat(parseFloat((deposit.amount/setting.initial_bonus * 100)) + parseFloat(deposit.amount), wallet.currency) }}  Bônus
                                        </div>
                                        <input id="placeholder-input" style="position: relative;padding-left: 38px;font-size: 15px;z-index: 1;background-color: #424344;" type="text"
                                                v-model="deposit.amount"
                                                class="appearance-none border border-gray-300 rounded-md bg-transparent border-none w-full"
                                                :min="setting.min_deposit"
                                                :max="setting.max_deposit"
                                                :placeholder="$t('0,00')"
                                                required />
                                    </div>
                                </div>
                            </div>

                            <div class="mt-5 flex items-center">
                                <i style="padding-left: 20px;position: absolute;z-index: 2;" class="fa-duotone fa-tachograph-digital"></i>
                                <input style="padding-left: 45px;position: relative;;font-size: 15px;z-index: 1;background-color: #424344;" type="text"
                                        v-model="deposit.cpf"
                                        v-maska
                                        data-maska="[
                                        '###.###.###-##',
                                        '##.###.###/####-##'
                                        ]"
                                        class="appearance-none border border-gray-300 rounded-md bg-transparent border-none w-full"
                                        placeholder="Digite o CPF"
                                        required>
                            </div>
                            <div class="mt-5 w-full flex items-center justify-center">
                                <button type="submit" class="ui-button-blue w-full p-4">
                                    <span class="uppercase text-sm" style="font-size: 1.2em;color: white;">{{ $t('GERAR PIX') }}</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div v-if="isLoading" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2 flex flex-col items-center loading-mobile-qr gap-3" >
            Gerando QR CODE..
            <i class="fa-duotone fa-spinner-third fa-spin" style="font-size: 45px;--fa-primary-color: var(--ci-primary-color); --fa-secondary-color: #000000;"></i>

        </div>

</template>

<script>
    import HttpApi from "@/Services/HttpApi.js";
import { useAuthStore } from "@/Stores/Auth.js";
import { useSettingStore } from "@/Stores/SettingStore.js";
import { StripeCheckout } from '@vue-stripe/vue-stripe';
import QRCodeVue3 from "qrcode-vue3";
import { useToast } from "vue-toastification";

export default {
    props: ['showMobile', 'title', 'isFull'],
    components: { QRCodeVue3, StripeCheckout },
    data() {
        return {
            isModalOpen: true,
            isLoading: false,
            minutes: 15,
            seconds: 0,
            timer: null,
            setting: null,
            wallet: null,
            deposit: {
                amount: '',
                cpf: '',
                gateway: '',
                accept_bonus: true
            },
            selectedAmount: 0,
            showPixQRCode: false,
            qrcodecopypast: '',
            idTransaction: '',
            intervalId: null,
            paymentType: null,

            /// stripe
            elementsOptions: {
                appearance: {}, // appearance options
            },
            confirmParams: {
                return_url: null, // success url
            },
            successURL: null,
            cancelURL: null,
            amount: null,
            currency: null,
            publishableKey: null,
            sessionId: null,
            paymentGateway: '',
        }
    },
    setup(props) {
        return {};
    },
    computed: {
        isAuthenticated() {
            const authStore = useAuthStore();
            return authStore.isAuth;
        },
    },
    mounted() {
        // Aguarda o setting estar disponível antes de configurar o método de pagamento
        this.$nextTick(() => {
            if (this.setting && this.setting.suitpay_is_enable) {
                this.setPaymentMethod('pix', this.setting.gateway.kind);
            }
        });
    },

    beforeUnmount() {
        clearInterval(this.timer);
        this.paymentType = null;
    },
    methods: {
        closeModal() {
            this.isModalOpen = false;
        },
        getSession: function() {
            const _this = this;
            HttpApi.post('stripe/session', { amount: _this.amount, currency: _this.currency}).then(response => {
                if(response.data.id) {
                    _this.sessionId = response.data.id;
                }
            }).catch(error => { });
        },
        checkoutStripe: function() {
            const _toast = useToast();
            if(this.amount <= 0 || this.amount === '') {
                _toast.error('Você precisa digitar um valor');
                return;
            }

            this.$refs.checkoutRef.redirectToCheckout();
        },
        getPublicKeyStripe: function() {
            const _this = this;
            HttpApi.post('stripe/publickey', {}).then(response => {
                _this.$nextTick(() => {
                    _this.publishableKey = response.data.stripe_public_key;
                    _this.elementsOptions.clientSecret  = response.data.stripe_secret_key;
                    _this.confirmParams.return_url      = response.data.successURL;
                });

            }).catch(error => { });
        },
        setPaymentMethod: function(type, gateway) {
            if(type === 'stripe') {
                this.getPublicKeyStripe();
            }
            this.paymentType = type;
            this.paymentGateway = gateway;
        },
        submitQRCode: function(event) {
            const _this = this;
            const _toast = useToast();
            if(_this.deposit.amount === '' || _this.deposit.amount === undefined) {
                _toast.error(_this.$t('You need to enter a value'));
                return;
            }

            if(_this.deposit.cpf === '' || _this.deposit.cpf === undefined) {
                _toast.error(_this.$t('Do you need to enter your CPF or CNPJ'));
                return;
            }

            if(parseFloat(_this.deposit.amount) < parseFloat(_this.setting.min_deposit)) {
                _toast.error('O valor mínimo de depósito é de '+ _this.setting.min_deposit);
                return;
            }

            if(parseFloat(_this.deposit.amount) > parseFloat(_this.setting.max_deposit)) {
                _toast.error('O valor máximo de depósito é de '+ _this.setting.min_deposit);
                return;
            }

            _this.deposit.paymentType = _this.paymentType;
            _this.deposit.gateway = _this.paymentGateway;

            _this.isLoading = true;
            HttpApi.post('wallet/deposit/payment', _this.deposit).then(response => {
                _this.showPixQRCode = true;
                _this.isLoading = false;

                _this.idTransaction = response.data.idTransaction;
                _this.qrcodecopypast = response.data.qrcode;

                _this.intervalId = setInterval(function () {
                    _this.checkTransactions(_this.idTransaction);
                }, 5000);

            }).catch(error => {
                Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                    _toast.error(`${value}`);
                });
                _this.showPixQRCode = false;
                _this.isLoading = false;
            });
        },
        checkTransactions: function(idTransaction) {
            const _this = this;
            const _toast = useToast();

            HttpApi.post(_this.paymentGateway+'/consult-status-transaction', { idTransaction: idTransaction }).then(response => {
                _toast.success('Pedido concluído com sucesso');
                clearInterval(_this.intervalId);
            }).catch(error => {
                Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                    // _toast.error(`${value}`);
                });
            });
        },
        copyQRCode: function(event) {
            const _toast = useToast();
            var inputElement = document.getElementById("pixcopiaecola");
            inputElement.select();
            inputElement.setSelectionRange(0, 99999);  // Para dispositivos móveis

            // Copia o conteúdo para a área de transferência
            document.execCommand("copy");
            _toast.success('Pix Copiado com sucesso');
        },
        setAmount: function(amount) {
            this.deposit.amount = amount;
            this.selectedAmount = amount;
        },
        getWallet: function() {
            const _this = this;
            const _toast = useToast();

            // Verifica cache para evitar requisições desnecessárias
            const cachedWallet = localStorage.getItem('wallet_cache');
            const cacheTime = localStorage.getItem('wallet_cache_time');
            const now = Date.now();

            // Cache válido por 30 segundos
            if (cachedWallet && cacheTime && (now - parseInt(cacheTime)) < 30000) {
                _this.wallet = JSON.parse(cachedWallet);
                _this.currency = _this.wallet.currency;
                return Promise.resolve();
            }

            _this.isLoadingWallet = true;

            return HttpApi.get('profile/wallet')
                .then(response => {
                    _this.wallet = response.data.wallet;
                    _this.currency = response.data.wallet.currency;
                    _this.isLoadingWallet = false;

                    // Salva no cache
                    localStorage.setItem('wallet_cache', JSON.stringify(response.data.wallet));
                    localStorage.setItem('wallet_cache_time', now.toString());
                })
                .catch(error => {
                    console.error('Erro ao carregar wallet:', error);
                    if (error.response && error.response.status === 401) {
                        localStorage.clear();
                    } else {
                        _toast.error('Erro ao carregar dados da carteira');
                    }
                    _this.isLoadingWallet = false;
                });
        },
        getSetting: function() {
            const _this = this;
            const settingStore = useSettingStore();
            const settingData = settingStore.setting;

            return new Promise((resolve) => {
                if(settingData) {
                    _this.setting = settingData;
                    _this.amount = settingData.max_deposit;

                    if(_this.paymentType === 'stripe' && settingData.stripe_is_enable) {
                        _this.getSession();
                    }
                    resolve(settingData);
                } else {
                    // Se não há setting no store, tenta buscar
                    settingStore.getSetting().then((data) => {
                        if (data && data.setting) {
                            _this.setting = data.setting;
                            _this.amount = data.setting.max_deposit;
                            resolve(data.setting);
                        } else {
                            resolve(null);
                        }
                    }).catch(() => {
                        resolve(null);
                    });
                }
            });
        },
    },
    created() {
        if(this.isAuthenticated) {
            // Carrega dados em paralelo para melhor performance
            Promise.all([
                this.getWallet(),
                this.getSetting()
            ]).then(() => {
                // Só configura stripe após os dados estarem carregados
                if(this.paymentType === 'stripe') {
                    this.getSession();
                    this.currency = 'USD';
                }
            }).catch(error => {
                console.error('Erro ao carregar dados do modal:', error);
            });
        }
    },
    watch: {
        amount(oldValue, newValue) {
            if(this.paymentType === 'stripe') {
                this.getSession();
                this.currency = 'USD';
            }
        },
        currency(oldValue, newValue) {
            if(this.paymentType === 'stripe') {
                this.getSession();
            }
        }
    },
};
</script>
<style scoped>
</style>
