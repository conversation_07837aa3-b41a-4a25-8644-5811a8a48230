<style>
  .btn-scroll-top {
    position: relative;
    z-index: 2;
    bottom: 2em;
    display: none;
    align-items: center;
    /* Outras propriedades de estilo aqui */
    background-color: #000000b3;
    border: 1px solid rgba(65, 105, 225, .7);
    color: #fff;
    cursor: pointer;
    transition: opacity 0.3s;
    font-size: 13px;
    gap: 0.5em;
    padding: 6px 12px;
    border-radius: 20px;
    margin-top: -30px;
    left: 48.5%;
    transform: translateX(-50%);
  }

  .categories-margin {
        margin-bottom: 15px;
    }

  .show-btn-scroll-top {
    display: block;
    display: flex;
  }

  /* Centralize verticalmente */
  .btn-scroll-top {
    /* transform: translateY(-50%); */
  }

  /* Centralize horizontalmente */
  .btn-scroll-top {

  }
  .search-mobile {
    -webkit-box-shadow: 0px 0px 14px -22px rgba(43,43,43,1);
-moz-box-shadow: 0px 0px 14px -22px rgba(43,43,43,1);
box-shadow: 0px 0px 14px -22px rgba(43,43,43,1);background-color: var(--ci-gray-dark);border-radius: 3px;font-size: 14px;padding-left: 40px;padding-top: 10px;padding-bottom: 10px;
  }
.margin-search {
    margin-top: -6px;
}
.placeholder-search::placeholder {
    color: #ACAFAF;
    font-size: 13px;
}
@media(max-width:1024px) {
    .btn-scroll-top {
        bottom: 5em;
        padding: 2px 7px;
    }
    .margin-categorias {
        margin-top: -20px;
    }
    .item-sombra2 {
        position: relative;

    }

    .item-sombra2:after {
        content: "";
        position: absolute;
        bottom: 0;
        margin-right: -1px;
        right: 0;
        width: 25px;
        height: 100%;
        background: linear-gradient(to right, rgb(33 36 37 / 0%), var(--carousel-banners));
    }
}

.padding-mobile-banner {
    padding-top: 100px;
}

@media (max-width:768px) {
    .categories-margin {
        margin-top: 0px;
    }
    #svg-lupa {
    margin-top: -2px;
    }
    .search-mobile {
    padding-top: 8px;
    padding-bottom: 8px;
    }
    .margin-search {
    margin-top: 0px;
}
    .padding-mobile-banner {
        padding: 0;
        padding-top: 40px;
    }
}

</style>
<template>
    <BaseLayout>
        <LoadingComponent :isLoading="isLoading">
            <div class="text-center"></div>
        </LoadingComponent>

        <div v-if="!isLoading" class="">

            <!-- Banners carousel -->
            <div class="carousel-banners">
                <div class="mx-auto w-full sm:max-w-[690px] lg:max-w-[1110px] p-4 padding-mobile-banner">
                         <!-- Conteúdo da sua página aqui -->
                         <div class="mx-auto w-full sm:max-w-[690px] lg:max-w-[1110px]" style="position: fixed;bottom: 0;z-index: 2;pointer-events: auto;height: 0px">
                         <button style="pointer-events: auto;" class="btn-scroll-top" @click="scrollToTop">
                            <div><svg height="1em" viewBox="0 0 384 512" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M214.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-160 160c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 141.2V448c0 17.7 14.3 32 32 32s32-14.3 32-32V141.2L329.4 246.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-160-160z" fill="currentColor"></path></svg></div> <div>De volta ao topo</div>
                            </button>
                        </div>
                    <div class="">
                        <Carousel v-bind="settings" :breakpoints="breakpoints" ref="carouselBanner">
                            <Slide v-for="(banner, index) in banners" :key="index">
                                <div class="carousel__item rounded w-full">
                                    <a :href="banner.link" class="w-full h-full bg-blue-800 rounded">
                                        <img :src="`/storage/`+banner.image" alt="" class="h-full w-full rounded" style="max-height: 413px">
                                    </a>
                                </div>
                            </Slide>


                            <template #addons>
                                <navigation>
                                    <template #next>
                                        <i style="font-size: 18px;" class="fa-solid fa-chevron-right text-white"></i>
                                    </template>
                                    <template #prev>
                                        <i style="font-size: 18px;" class="fa-solid fa-chevron-left text-white"></i>
                                    </template>
                                </navigation>

                            </template>
                        </Carousel>
                    </div>
                </div>
            </div>

            <div class="w-full p-4">
                <!-- categories -->
                <div  class="category-list item-sombra2 margin-categorias categories-margin">
                    <div class="flex gap-1 " style="max-height: 200px; overflow-x: auto; overflow-y: hidden;margin-left: -10px;">
                        <div class="flex flex-row justify-between items-center w-full " style="min-width: 100%; white-space: nowrap;">

                            <template v-for="(category, index) in categories" :key="index">
                                <RouterLink
                                    v-if="!category.url"
                                    :to="{ name: 'casinosAll', params: { provider: 'all', category: category.slug }}"
                                    class="flex flex-col justify-center items-center min-w-[80px] text-center">
                                    <div class="flex items-center justify-center p-4" style="background-color: var(--ci-primary-opacity-color);border-radius: 50%;">
                                        <img style="color: blue" :src="`/storage/`+category.image" alt="" width="35" height="35" class="">
                                    </div>
                                    <p class="mt-1 flex flex-wrap wrap" style="font-size: .75rem;">{{ $t(category.name) }}</p>
                                </RouterLink>
                                <a
                                    v-else
                                    :href="category.url"
                                    class="flex flex-col justify-center items-center min-w-[80px] text-center">
                                    <div class="flex items-center justify-center p-4" style="background-color: var(--ci-primary-opacity-color);border-radius: 50%;">
                                        <img style="color: blue" :src="`/storage/`+category.image" alt="" width="35" height="35" class="">
                                    </div>
                                    <p class="mt-1 flex flex-wrap wrap" style="font-size: .75rem;">{{ $t(category.name) }}</p>
                                </a>
                            </template>

                        </div>
                    </div>
                </div>

                <div class="mx-auto sm:max-w-[690px] lg:max-w-[1110px] w-full search-margin">
                <!-- Searchbar action -->
                <div class=" cursor-pointer w-full margin-search" style="margin-bottom: 39px;">
                    <div class="flex">
                        <div class="relative w-full">
                            <button @click="toggleSearch" type="submit" class="absolute top-0 start-0 h-full p-2.5 text-sm font-medium text-white rounded-e-lg">
                                <svg id="svg-lupa" data-v-49f1cded="" height="14px" viewBox="0 0 512 512" width="14px" xmlns="http://www.w3.org/2000/svg" class="text-texts w-5 p-0 m-0" style="width: 20px; height: 16px;"><path d="M500.3 443.7l-119.7-119.7c-15.03 22.3-34.26 41.54-56.57 56.57l119.7 119.7c15.62 15.62 40.95 15.62 56.57 0C515.9 484.7 515.9 459.3 500.3 443.7z" fill="currentColor"></path><path d="M207.1 0C93.12 0-.0002 93.13-.0002 208S93.12 416 207.1 416s208-93.13 208-208S322.9 0 207.1 0zM207.1 336c-70.58 0-128-57.42-128-128c0-70.58 57.42-128 128-128s128 57.42 128 128C335.1 278.6 278.6 336 207.1 336z" fill="currentColor" opacity="0.4"></path></svg>
                                <span class="sr-only">Search</span>
                            </button>
                            <input class="w-full placeholder-search p-2.5 lg:p-0 search-mobile" @click.prevent="toggleSearch" type="search"
                                   readonly
                                   placeholder="Pesquise um jogo de cassino..." required>


                        </div>
                    </div>
               </div>
               </div>

                <div class="mt-10">
                    <Carousel v-bind="settingsRecommended" :breakpoints="breakpointsRecommended"
                        ref="carouselSubBanner">
                        <Slide v-for="(banner, index) in bannersHome" :key="index">
                            <div class="carousel__item  min-h-[60px] md:min-h-[150px] rounded-2xl w-full mr-4">
                                <a :href="banner.link" class="w-full h-full rounded-2xl">
                                    <img :src="`/storage/`+banner.image" alt="" class="h-full w-full rounded-2xl">
                                </a>
                            </div>
                        </Slide>

                        <template #addons>

                        </template>
                    </Carousel>
                </div>


                <div v-if="featured_games" class="mt-3">

                    <div class="w-full flex justify-between mb-2">
                        <div class="flex items-center">
                            <h2 class="text-lg" style="color: white">{{ $t('Recomendados') }}</h2>
                            <button @click.prevent="fgCarousel.prev()"
                                class="item-game px-2 py-1 rounded-lg text-[12px] ml-2 mr-2 celular-providers-setas">
                                <i class="fa-solid fa-angle-left"></i>
                            </button>
                            <button @click.prevent="fgCarousel.next()"
                                class="item-game px-2 py-1 rounded-lg text-[12px] celular-providers-setas">
                                <i class="fa-solid fa-angle-right"></i>
                            </button>
                        </div>
                        <div class="flex">

                        </div>
                    </div>

                    <Carousel class="item-sombra2 " ref="fgCarousel" v-bind="settingsGames" :breakpoints="breakpointsGames"
                        @init="onCarouselInit(index)" @slide-start="onSlideStart(index)">
                        <Slide v-if="isLoading" v-for="(i, iloading) in 10" :index="iloading">
                            <div role="status"
                                class=" w-full flex items-center justify-center h-48 mr-6 max-w-sm bg-gray-300 rounded-lg animate-pulse dark:bg-gray-700 text-4xl">
                                <i class="fa-duotone fa-gamepad-modern"></i>
                            </div>
                        </Slide>

                        <Slide v-if="featured_games && !isLoading" v-for="(game, providerId) in featured_games"
                            :index="providerId" class="p-2">
                            <CassinoGameCard :index="providerId" :title="game.game_name" :cover="game.cover"
                                :gamecode="game.game_code" :type="game.distribution" :game="game" />
                        </Slide>
                    </Carousel>
                </div>


                <!-- <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mb-5">
                        <CassinoGameCard
                            v-for="(game, index) in featured_games"
                            :index="index"
                            :title="game.game_name"
                            :cover="game.cover"
                            :gamecode="game.game_code"
                            :type="game.distribution"
                            :game="game"
                        />
                    </div> -->

                <div class="mt-5 ">
                    <ShowProviders v-for="(provider, index) in providers" :provider="provider" :index="index" />
                </div>


            </div>
        </div>
    </BaseLayout>
</template>

<script>
import CustomPagination from "@/Components/UI/CustomPagination.vue";
import LanguageSelector from "@/Components/UI/LanguageSelector.vue";
import LoadingComponent from "@/Components/UI/LoadingComponent.vue";
import MakeDeposit from "@/Components/UI/MakeDeposit.vue";
import BaseLayout from "@/Layouts/BaseLayout.vue";
import CassinoGameCard from "@/Pages/Cassino/Components/CassinoGameCard.vue";
import ShowCarousel from "@/Pages/Home/Components/ShowCarousel.vue";
import ShowProviders from "@/Pages/Home/Components/ShowProviders.vue";
import HttpApi from "@/Services/HttpApi.js";
import { useAuthStore } from "@/Stores/Auth.js";
import { searchGameStore } from "@/Stores/SearchGameStore.js";
import { useSettingStore } from "@/Stores/SettingStore.js";
import { onMounted, ref } from "vue";
import { RouterLink } from "vue-router";
import { Carousel, Navigation, Pagination, Slide } from 'vue3-carousel';

export default {
    props: [],
    components: {
        CustomPagination,
        Pagination,
        ShowProviders,
        LoadingComponent,
        ShowCarousel,
        CassinoGameCard,
        Carousel,
        Navigation,
        Slide,
        LanguageSelector,
        MakeDeposit,
        BaseLayout,
        RouterLink
    },
    data() {
        return {
            isLoading: true,

            /// banners settings
            settings: {
                itemsToShow: 1,
                snapAlign: 'center',
                autoplay: 6000,
                wrapAround: true
            },
            breakpoints: {
                700: {
                    itemsToShow: 1,
                    snapAlign: 'center',
                },
                1024: {
                    itemsToShow: 1,
                    snapAlign: 'center',
                },
            },

            settingsRecommended: {
                itemsToShow: 2,
                snapAlign: 'start',
            },
            breakpointsRecommended: {
                700: {
                    itemsToShow: 3,
                    snapAlign: 'center',
                },
                1024: {
                    itemsToShow: 3,
                    snapAlign: 'start',
                },
            },

            /// banners
            banners: null,
            bannersHome: null,

            settingsGames: {
                itemsToShow: 2.5,
                snapAlign: 'start',
            },
            breakpointsGames: {
                700: {
                    itemsToShow: 3.5,
                    snapAlign: 'center',
                },
                1024: {
                    itemsToShow: 6,
                    snapAlign: 'start',
                },
            },
            settingsImages: {
                itemsToShow: 2,
                snapAlign: 'start',
            },
            providers: null,

            featured_games: null,
            categories: null,
        }
    },
    setup(props) {
        const ckCarouselOriginals = ref(null)
        const fgCarousel = ref(null)

        onMounted(() => {

        });

        return {
            ckCarouselOriginals,
            fgCarousel
        };

    },
    computed: {
        searchGameStore() {
            return searchGameStore();
        },
        userData() {
            const authStore = useAuthStore();
            return authStore.user;
        },
        isAuthenticated() {
            const authStore = useAuthStore();
            return authStore.isAuth;
        },
        setting() {
            const settingStore = useSettingStore();
            return settingStore.setting;
        }
    },
    mounted() {
        window.scrollTo(0, 0);
        window.addEventListener('scroll', () => {
            const btnScrollTop = document.querySelector('.btn-scroll-top');
            if (btnScrollTop) {
                if (window.scrollY > 100) {
                    btnScrollTop.classList.add('show-btn-scroll-top');
                } else {
                    btnScrollTop.classList.remove('show-btn-scroll-top');
                }
            }
        });
    },
    methods: {
        scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    },
        getCasinoCategories: async function () {
            const _this = this;
            await HttpApi.get('categories')
                .then(response => {
                    _this.categories = response.data.categories;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {

                    });
                });
        },
        toggleSearch: function () {
            this.searchGameStore.setSearchGameToogle();
        },
        getBanners: async function () {
            const _this = this;

            try {
                const response = await HttpApi.get('settings/banners');
                const allBanners = response.data.banners;

                _this.banners = allBanners.filter(banner => banner.type === 'carousel');
                _this.bannersHome = allBanners.filter(banner => banner.type === 'home');
            } catch (error) {
                console.error(error);
            } finally {

            }
        },
        getAllGames: async function () {
            const _this = this;
            return await HttpApi.get('games/all')
                .then(async response => {
                    if (response.data !== undefined) {
                        _this.providers = response.data.providers;
                        _this.isLoading = false;
                    }
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        console.log(`${value}`);
                    });
                    _this.isLoading = false;
                });
        },
        getFeaturedGames: async function () {
            const _this = this;
            return await HttpApi.get('featured/games')
                .then(async response => {
                    _this.featured_games = response.data.featured_games;
                    _this.isLoading = false;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        console.log(`${value}`);
                    });
                    _this.isLoading = false;
                });
        },
        initializeMethods: async function () {
            await this.getCasinoCategories();
            await this.getBanners();
            await this.getAllGames();
            await this.getFeaturedGames();
        },
        onCarouselInit(index) {},
        onSlideStart(index) {},
    },
    async created() {
        await this.initializeMethods();
    },
    watch: {},
};

</script>
